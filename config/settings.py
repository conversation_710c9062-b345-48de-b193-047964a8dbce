import os
import importlib.util
from dotenv import load_dotenv

# Check if torch is available
torch_available = importlib.util.find_spec("torch") is not None
if torch_available:
    import torch

# Load environment variables from .env file
load_dotenv()

# OpenAI API settings
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    raise ValueError("OPENAI_API_KEY environment variable is not set. Please create a .env file with your API key.")

VIDEO_WIDTH = 720  # Increased width for 9:16 aspect ratio (vertical video)
VIDEO_HEIGHT = 1280  # Increased height for 9:16 aspect ratio (vertical video)
VIDEO_FPS = 30  # Higher frame rate for smoother short-form videos
VIDEO_FORMAT = "vertical"  # Options: "horizontal", "vertical", "square"



# Caption settings
CAPTION_ENABLED = True
CAPTION_FONT_SIZE = 18  # Optimized for mobile readability on 720x1280 resolution
CAPTION_FONT_COLOR = "white"
CAPTION_BG_COLOR = "rgba(0,0,0,0.7)"  # More visible background
CAPTION_POSITION = "top"  # Options: "top", "bottom", "center" - Moved to top for better visibility and engagement
CAPTION_STYLE = "bold"  # Options: "modern", "minimal", "bold", "playful", "elegant"
CAPTION_ANIMATION = True  # Enable animation effects
CAPTION_ANIMATION_STYLE = "pop"  # Changed to more engaging pop animation
CAPTION_FONT = "Poppins-Bold"  # Modern, bold font for better visibility and style
CAPTION_WIDTH_PERCENT = 90  # Further reduced width for smaller captions
CAPTION_EMOJI_ENABLED = True  # Use emojis in captions

# Multi-language caption support
CAPTION_LANGUAGE_DETECTION = True  # Enable automatic language detection
CAPTION_FONT_FALLBACKS = {
    "hindi": "NotoSansDevanagari-Bold",  # Hindi/Devanagari script
    "arabic": "NotoSansArabic-Bold",     # Arabic script
    "chinese": "NotoSansCJK-Bold",       # Chinese/Japanese/Korean
    "default": "Poppins-Bold"            # Default Latin script
}
CAPTION_UNICODE_SUPPORT = True  # Enhanced Unicode text handling
CAPTION_HASHTAGS_ENABLED = True  # Include trending hashtags

# Enhanced Caption Settings for Highlight Videos
CAPTION_MAX_LINES = 2  # Maximum lines for optimal readability in short-form content
CAPTION_LINE_HEIGHT_MULTIPLIER = 1.1  # Optimized line height for readability without excessive spacing
CAPTION_LINE_SPACING = 8  # Fixed line spacing in pixels (independent of font size)
CAPTION_PADDING_HORIZONTAL = 16  # Horizontal padding for better text breathing room
CAPTION_PADDING_VERTICAL = 12  # Vertical padding for better text breathing room
CAPTION_MARGIN_FROM_EDGE = 40  # Margin from video edges for safe area
CAPTION_FACE_AVOIDANCE_MARGIN = 50  # Face avoidance margin
CAPTION_DYNAMIC_POSITIONING = True  # Enable face-aware positioning
CAPTION_WORD_WRAP_ALGORITHM = "smart"  # Options: "simple", "smart", "balanced"
CAPTION_FONT_WEIGHT = "bold"  # Font weight for better visibility
CAPTION_STROKE_WIDTH = 2  # Stroke width for better contrast
CAPTION_STROKE_COLOR = "black"  # Text stroke color
CAPTION_SHADOW_ENABLED = True  # Enable text shadow
CAPTION_SHADOW_OFFSET_X = 2  # Shadow horizontal offset
CAPTION_SHADOW_OFFSET_Y = 2  # Shadow vertical offset
CAPTION_SHADOW_BLUR = 4  # Shadow blur radius
CAPTION_SHADOW_COLOR = "rgba(0,0,0,0.8)"  # Shadow color

# Advanced Caption Sizing
CAPTION_AUTO_SCALE = True  # Enable automatic font scaling based on content
CAPTION_MIN_FONT_SIZE = 8  # Minimum font size for readability
CAPTION_MAX_FONT_SIZE = 48  # Maximum font size to prevent overflow
CAPTION_CHAR_WIDTH_RATIO = 0.6  # Character width to font size ratio for calculations
CAPTION_LANGUAGE_SCALING = {
    "hindi": 1.1,  # Slightly larger for Devanagari script readability
    "arabic": 1.1,  # Slightly larger for Arabic script
    "chinese": 1.0,  # Standard size for CJK characters
    "default": 1.0  # Standard size for Latin scripts
}

# Fine-grained Vertical Spacing Control using ScaleY
CAPTION_SCALE_Y = 60  # Vertical scaling percentage (85% = tighter spacing, 100% = normal, 115% = looser)
CAPTION_USE_SCALE_Y = True  # Enable ScaleY for precise vertical gap control
CAPTION_VERTICAL_COMPRESSION = 0.85  # Alternative: direct compression ratio for line height

# Precise Caption Positioning Control
CAPTION_MARGIN_V = 160  # Vertical margin in pixels (distance from top/bottom edge)
CAPTION_MARGIN_L = 16  # Left margin in pixels
CAPTION_MARGIN_R = 16  # Right margin in pixels
CAPTION_POSITION_FINE_TUNE = True  # Enable fine-tuned positioning control

# File paths
OUTPUT_DIR = "output"
TEMP_DIR = "temp"

# Core video processing settings
WHISPER_MODEL = "whisper-1"  # OpenAI's Whisper model for transcription
MAX_VIDEO_DURATION_SECONDS = 14400  # Maximum duration in seconds (240 minutes / 4 hours)
MAX_HIGHLIGHT_DURATION_SECONDS = 60  # Maximum duration for highlight videos
MIN_HIGHLIGHT_DURATION_SECONDS = 10  # Minimum duration for highlight videos (enforced across all highlight processing)
DEFAULT_CONTEXT_WINDOW_SECONDS = 10  # Default context window around keyword mentions



# Smart Video Highlight Generator Pipeline settings
PIPELINE_OUTPUT_DIR = OUTPUT_DIR
PIPELINE_METRICS_ENABLED = os.getenv("PIPELINE_METRICS_ENABLED", "True").lower() == "true"
PIPELINE_GPU_ENABLED = os.getenv("PIPELINE_GPU_ENABLED", "True").lower() == "true" and torch_available and torch.cuda.is_available()
PIPELINE_RESUME_ENABLED = os.getenv("PIPELINE_RESUME_ENABLED", "True").lower() == "true"
PIPELINE_MAX_VIDEO_DURATION = int(os.getenv("PIPELINE_MAX_VIDEO_DURATION", "14400"))  # 240 minutes (4 hours)
PIPELINE_MIN_HIGHLIGHT_SCORE = float(os.getenv("PIPELINE_MIN_HIGHLIGHT_SCORE", "0.5"))

PIPELINE_EMBEDDING_MODEL = os.getenv("PIPELINE_EMBEDDING_MODEL", "all-MiniLM-L6-v2")

# Intelligent Highlights Extractor settings
INTELLIGENT_HIGHLIGHTS_TARGET_LENGTH = float(os.getenv("INTELLIGENT_HIGHLIGHTS_TARGET_LENGTH", "75.0"))  # Default 75 seconds
INTELLIGENT_HIGHLIGHTS_MIN_SPAN = float(os.getenv("INTELLIGENT_HIGHLIGHTS_MIN_SPAN", "10.0"))  # Minimum span duration
INTELLIGENT_HIGHLIGHTS_MAX_SPAN = float(os.getenv("INTELLIGENT_HIGHLIGHTS_MAX_SPAN", "30.0"))  # Maximum span duration
INTELLIGENT_HIGHLIGHTS_PADDING = float(os.getenv("INTELLIGENT_HIGHLIGHTS_PADDING", "1.0"))  # Padding around spans

# OpenAI Enhanced Highlights Scoring settings (Cost-Optimized)
OPENAI_HIGHLIGHTS_ENABLED = os.getenv("OPENAI_HIGHLIGHTS_ENABLED", "False").lower() == "true"  # Disabled by default for cost control
OPENAI_HIGHLIGHTS_MODEL = os.getenv("OPENAI_HIGHLIGHTS_MODEL", "gpt-4o-mini")  # Cost-effective model
OPENAI_HIGHLIGHTS_MAX_REQUESTS = int(os.getenv("OPENAI_HIGHLIGHTS_MAX_REQUESTS", "20"))  # Conservative limit
OPENAI_HIGHLIGHTS_TOP_CANDIDATES = int(os.getenv("OPENAI_HIGHLIGHTS_TOP_CANDIDATES", "20"))  # Only enhance top N spans

# Stage-08 Re-framer Face Detection Settings
FACE_DETECTION_ENABLED = os.getenv("FACE_DETECTION_ENABLED", "True").lower() == "true"
FACE_DETECTION_BACKEND = os.getenv("FACE_DETECTION_BACKEND", "mediapipe")  # Options: "auto", "mediapipe", "insightface"
FACE_DETECTION_CONFIDENCE = float(os.getenv("FACE_DETECTION_CONFIDENCE", "0.5"))  # Minimum confidence for face detection
FACE_DETECTION_GPU_ENABLED = os.getenv("FACE_DETECTION_GPU_ENABLED", "True").lower() == "true" and torch_available and torch.cuda.is_available()  # Enable GPU acceleration
FACE_TRACKING_SMOOTHING = float(os.getenv("FACE_TRACKING_SMOOTHING", "0.4"))  # Temporal smoothing factor (0-1) - increased for smoother tracking
FACE_CROP_MARGIN = float(os.getenv("FACE_CROP_MARGIN", "0.2"))  # Additional margin around detected faces (0-1)
SPEAKER_IDENTIFICATION_ENABLED = os.getenv("SPEAKER_IDENTIFICATION_ENABLED", "True").lower() == "true"
MOTION_FALLBACK_ENABLED = os.getenv("MOTION_FALLBACK_ENABLED", "True").lower() == "true"
CROP_TRANSITION_DURATION = float(os.getenv("CROP_TRANSITION_DURATION", "1.0"))  # Seconds for smooth transitions
PIPELINE_FORCE_REPROCESS = os.getenv("PIPELINE_FORCE_REPROCESS", "").split(",")  # Comma-separated list of stages to force reprocessing
PIPELINE_CLEANUP_ENABLED = os.getenv("PIPELINE_CLEANUP_ENABLED", "False").lower() == "true"  # Enable/disable cleanup in Cleanup & Metrics stage

# Create directories if they don't exist
os.makedirs(OUTPUT_DIR, exist_ok=True)
os.makedirs(TEMP_DIR, exist_ok=True)
os.makedirs(PIPELINE_OUTPUT_DIR, exist_ok=True)
