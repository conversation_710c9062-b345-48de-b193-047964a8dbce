#!/usr/bin/env python3
"""
Sequential Pipeline Runner - Execute all 15 stages one by one in a single thread
"""

import os
import sys
import uuid
import time
import logging
import json
import argparse
import shutil
from pathlib import Path
from typing import Optional, Dict, Any

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the project root to the path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from pipeline.tasks.preflight_validator import PreflightValidator
    from pipeline.tasks.video_ingestor import VideoIngestor
    from pipeline.tasks.transcription_engine import TranscriptionEngine
    from pipeline.tasks.world_class_highlights_extractor import AdvancedHighlightsExtractor
    from highlight_extraction.pipeline import QAHighlightsTask as QAHighlightsExtractor
    from pipeline.tasks.clip_renderer import <PERSON><PERSON><PERSON><PERSON><PERSON>
    from pipeline.tasks.reframer import Reframer
    from pipeline.tasks.caption_composer import Caption<PERSON>omposer
    from pipeline.tasks.deliverables_publisher import DeliverablesPublisher
    from pipeline.tasks.cleanup_metrics import CleanupMetrics

    from pipeline.job_state_manager import get_job_state_manager
    from config.settings import PIPELINE_OUTPUT_DIR
except ImportError as e:
    print(f"❌ Import Error: {e}")
    print("Make sure all pipeline tasks are properly implemented.")
    sys.exit(1)

# Define base pipeline stages for world-class highlights
BASE_PIPELINE_STAGES = [
    {'name': 'preflight_validator', 'display': 'Pre-flight Validator', 'class': PreflightValidator},
    {'name': 'video_ingestor', 'display': 'Video Ingestor', 'class': VideoIngestor},
    {'name': 'transcription_engine', 'display': 'Transcription Engine', 'class': TranscriptionEngine},
    {'name': 'world_class_highlights_extractor', 'display': 'Advanced Highlights Extractor', 'class': AdvancedHighlightsExtractor},
]

# Optional QA stages (can be enabled/disabled)
QA_PIPELINE_STAGES = [
    {'name': 'qa_highlights_extractor', 'display': 'QA Highlights Extractor', 'class': QAHighlightsExtractor},
    # QA Pair Segment Extractor not yet moved to new structure
    # {'name': 'qa_pair_segment_extractor', 'display': 'QA Pair Segment Extractor', 'class': QAPairSegmentExtractor},
]

# Final processing stages
FINAL_PIPELINE_STAGES = [
    {'name': 'clip_renderer', 'display': 'Clip Renderer', 'class': ClipRenderer},
    {'name': 'reframer', 'display': 'Re-framer', 'class': Reframer},
    {'name': 'caption_composer', 'display': 'Caption & Graphics Composer', 'class': CaptionComposer},
    {'name': 'deliverables_publisher', 'display': 'Deliverables Publisher', 'class': DeliverablesPublisher},
    {'name': 'cleanup_metrics', 'display': 'Cleanup & Metrics', 'class': CleanupMetrics}
]

def get_pipeline_stages(enable_qa_highlights: bool) -> list:
    """Get the appropriate pipeline stages based on configuration."""
    stages = BASE_PIPELINE_STAGES.copy()

    if enable_qa_highlights:
        stages.extend(QA_PIPELINE_STAGES)

    stages.extend(FINAL_PIPELINE_STAGES)
    return stages

def find_existing_job(video_path: str, keywords: list) -> Optional[str]:
    """Find existing job for the same video and keywords to enable resume"""
    if not os.path.exists(PIPELINE_OUTPUT_DIR):
        return None

    for job_dir in os.listdir(PIPELINE_OUTPUT_DIR):
        job_path = os.path.join(PIPELINE_OUTPUT_DIR, job_dir)
        params_file = os.path.join(job_path, "params.json")

        if os.path.exists(params_file):
            try:
                with open(params_file, 'r') as f:
                    job_params = json.load(f)

                # For resume functionality, match video path and keywords
                # Sort keywords for consistent comparison (in case order differs)
                existing_keywords = sorted(job_params.get('keywords', []))
                current_keywords = sorted(keywords)

                if (job_params.get('video_path') == video_path and
                    existing_keywords == current_keywords):
                    return job_dir
            except:
                continue

    return None

def create_highlights_directory(job_id: str, stage_results: Dict[str, Any]) -> str:
    """
    Create a unified highlights directory with all separate clips

    Args:
        job_id: Job ID for the current pipeline run
        stage_results: Results from all pipeline stages

    Returns:
        Path to the created highlights directory
    """
    try:
        # Create highlights directory
        job_dir = os.path.join(PIPELINE_OUTPUT_DIR, job_id)
        highlights_dir = os.path.join(job_dir, "highlights")
        os.makedirs(highlights_dir, exist_ok=True)

        clips_copied = 0

        # Copy clips from clip renderer
        clip_result = stage_results.get("clip_renderer")
        if clip_result and clip_result.get("clips"):
            for clip in clip_result["clips"]:
                clip_path = clip.get("file_path")
                if clip_path and os.path.exists(clip_path):
                    filename = os.path.basename(clip_path)
                    dest_path = os.path.join(highlights_dir, f"highlight_{filename}")
                    shutil.copy2(clip_path, dest_path)
                    clips_copied += 1
                    logger.info(f"📁 Copied clip to highlights: {filename}")

        # Copy QA segments if available
        qa_result = stage_results.get("qa_pair_segment_extractor")
        if qa_result and qa_result.get("qa_segments"):
            for segment in qa_result["qa_segments"]:
                segment_path = segment.get("file_path")
                if segment_path and os.path.exists(segment_path):
                    filename = os.path.basename(segment_path)
                    dest_path = os.path.join(highlights_dir, f"qa_{filename}")
                    shutil.copy2(segment_path, dest_path)
                    clips_copied += 1
                    logger.info(f"📁 Copied Q&A segment to highlights: {filename}")

        # Copy reframed clips if available
        reframer_result = stage_results.get("reframer")
        if reframer_result and reframer_result.get("metadata", {}).get("clips"):
            for clip in reframer_result["metadata"]["clips"]:
                reframed_path = clip.get("reframed_path")
                if reframed_path and os.path.exists(reframed_path):
                    filename = os.path.basename(reframed_path)
                    dest_path = os.path.join(highlights_dir, f"reframed_{filename}")
                    shutil.copy2(reframed_path, dest_path)
                    clips_copied += 1
                    logger.info(f"📁 Copied reframed clip to highlights: {filename}")

        logger.info(f"✅ Created highlights directory with {clips_copied} separate clips")
        logger.info(f"📂 Highlights directory: {highlights_dir}")

        return highlights_dir

    except Exception as e:
        logger.warning(f"Failed to create highlights directory: {str(e)}")
        return ""





def copy_sample_transcript_to_job_dir(video_path: str, job_id: str) -> bool:
    """
    Copy pre-generated transcript files from sample directory to job directory
    to avoid unnecessary API calls for transcription

    Args:
        video_path: Path to the input video file
        job_id: Job ID for the current pipeline run

    Returns:
        True if transcript files were copied, False otherwise
    """
    try:
        # Check if this is a sample video with pre-generated transcripts
        video_dir = os.path.dirname(video_path)
        sample_transcript_dir = os.path.join(video_dir, "transcript")

        if not os.path.exists(sample_transcript_dir):
            logger.info(f"No sample transcript directory found at {sample_transcript_dir}")
            return False

        # Create job transcript directory
        job_dir = os.path.join(PIPELINE_OUTPUT_DIR, job_id)
        job_transcript_dir = os.path.join(job_dir, "transcript")
        os.makedirs(job_transcript_dir, exist_ok=True)

        # Copy all transcript files from sample to job directory
        copied_files = []
        for filename in os.listdir(sample_transcript_dir):
            src_file = os.path.join(sample_transcript_dir, filename)
            if os.path.isfile(src_file):
                dst_file = os.path.join(job_transcript_dir, filename)
                shutil.copy2(src_file, dst_file)
                copied_files.append(filename)
                logger.info(f"💰 Copied pre-generated transcript file: {filename}")

        if copied_files:
            logger.info(f"✅ Successfully copied {len(copied_files)} transcript files to avoid API calls")
            logger.info(f"💵 This saves money by reusing existing transcriptions!")
            return True
        else:
            logger.info("No transcript files found in sample directory")
            return False

    except Exception as e:
        logger.warning(f"Failed to copy sample transcript files: {str(e)}")
        return False

def run_sequential_pipeline_with_resume(video_path: str, keywords: list, params: Optional[dict] = None, resume: bool = True):
    """
    Run all pipeline stages sequentially with resume functionality to save money and time.

    This function implements a robust pipeline execution with comprehensive error handling,
    resume functionality, and optimized processing for testing scenarios.

    Args:
        video_path: Path to the input video file (must exist and be readable)
        keywords: List of keywords to search for highlights (can be empty for auto-detection)
        params: Additional parameters for pipeline configuration
        resume: Whether to resume from existing job if found (saves time and API costs)

    Returns:
        Dict containing stage results if successful, None if failed

    Raises:
        ValueError: If video_path is invalid or parameters are malformed
        FileNotFoundError: If video file doesn't exist
    """
    # Input validation with detailed error messages
    if not video_path:
        raise ValueError("video_path cannot be empty or None")

    if not os.path.exists(video_path):
        raise FileNotFoundError(f"Video file not found: {video_path}")

    if not os.path.isfile(video_path):
        raise ValueError(f"Path is not a file: {video_path}")

    # Validate video file is readable
    try:
        with open(video_path, 'rb') as f:
            f.read(1024)  # Try to read first 1KB
    except Exception as e:
        raise ValueError(f"Video file is not readable: {str(e)}")

    # Initialize parameters with safe defaults
    params = params or {}

    # Validate keywords parameter
    if not isinstance(keywords, list):
        raise ValueError("keywords must be a list")

    logger.info(f"Starting pipeline with video: {video_path}")
    logger.info(f"Keywords: {keywords if keywords else 'Auto-detection mode'}")
    logger.info(f"Resume enabled: {resume}")
    logger.info(f"Parameters: {len(params)} custom settings")

    # Try to find existing job if resume is enabled
    job_id = None
    if resume:
        job_id = find_existing_job(video_path, keywords)
        if job_id:
            print(f"🔄 Found existing job: {job_id}")
            print(f"💰 Resuming to save money and time!")
        else:
            print("🆕 No existing job found, starting new pipeline")

    # Create new job if no existing job found
    if not job_id:
        job_id = str(uuid.uuid4())

        # Create job directory and save parameters
        job_dir = os.path.join(PIPELINE_OUTPUT_DIR, job_id)
        os.makedirs(job_dir, exist_ok=True)

        job_params = {
            'job_id': job_id,
            'video_path': video_path,
            'keywords': keywords,
            'params': params
        }
        #
        params_path = os.path.join(job_dir, "params.json")
        with open(params_path, 'w') as f:
            json.dump(job_params, f, indent=2)

        # Copy sample transcript files to job directory to avoid API calls
        transcript_copied = copy_sample_transcript_to_job_dir(video_path, job_id)
        if transcript_copied:
            print(f"💰 Pre-generated transcripts copied - API calls will be avoided!")
        else:
            print(f"ℹ️  No pre-generated transcripts found - will use API for transcription")
    else:
        # For existing jobs, also try to copy transcript files if they don't exist
        job_dir = os.path.join(PIPELINE_OUTPUT_DIR, job_id)
        job_transcript_dir = os.path.join(job_dir, "transcript")
        transcript_json_path = os.path.join(job_transcript_dir, "transcript.json")

        if not os.path.exists(transcript_json_path):
            print(f"🔍 Checking for pre-generated transcripts for existing job...")
            transcript_copied = copy_sample_transcript_to_job_dir(video_path, job_id)
            if transcript_copied:
                print(f"💰 Pre-generated transcripts copied to existing job - API calls will be avoided!")

    print(f"🚀 Sequential Pipeline with Resume")
    print(f"📁 Job ID: {job_id}")
    print(f"🎬 Video: {video_path}")
    print(f"🔍 Keywords: {', '.join(keywords)}")
    print("=" * 80)

    start_time = time.time()
    stage_results = {}

    # Get job state manager for resume functionality
    state_manager = get_job_state_manager(job_id)

    # Get the appropriate pipeline stages based on QA configuration
    enable_qa_highlights = params.get("enable_qa_highlights", False)
    pipeline_stages = get_pipeline_stages(enable_qa_highlights)

    try:
        # Load existing stage results if resuming
        if resume:
            try:
                existing_results = state_manager.get_all_stage_results()
                stage_results.update(existing_results)
                completed_stages = state_manager.get_job_state().get('completed_stages', [])
                print(f"💾 Loaded {len(existing_results)} existing stage results")
                print(f"✅ Completed stages: {len(completed_stages)}/{len(pipeline_stages)}")
                logger.info(f"Resume: Loaded {len(existing_results)} stage results")
            except Exception as e:
                logger.warning(f"Failed to load existing results: {str(e)}")
                print(f"⚠️  Could not load existing results, starting fresh")

        # Execute all stages in order with comprehensive error handling
        for i, stage in enumerate(pipeline_stages):
            stage_name = stage['name']
            stage_display = stage['display']
            stage_class = stage['class']

            logger.info(f"Processing stage {i+1}/{len(pipeline_stages)}: {stage_name}")

            # Check if stage is already completed (resume functionality)
            if resume and state_manager.is_stage_completed(stage_name):
                try:
                    existing_result = state_manager.get_stage_result(stage_name)
                    if existing_result:
                        stage_results[stage_name] = existing_result
                        print(f"⏭️  Stage {i+1}: {stage_display} - SKIPPED (already completed)")
                        logger.info(f"Skipped completed stage: {stage_name}")
                        continue
                except Exception as e:
                    logger.warning(f"Failed to load existing result for {stage_name}: {str(e)}")
                    print(f"⚠️  Could not load existing result for {stage_name}, re-running")

            print(f"🔄 Stage {i+1}: {stage_display}")

            # Create stage instance with error handling
            try:
                processor = stage_class()
                logger.info(f"Created processor for stage: {stage_name}")
            except Exception as e:
                error_msg = f"Failed to create processor for stage {stage_name}: {str(e)}"
                logger.error(error_msg)
                raise ValueError(error_msg)

            # Run stage with appropriate arguments based on dependencies
            if stage_name == "preflight_validator":
                result = processor.run(job_id, video_path, params)
            elif stage_name == "video_ingestor":
                result = processor.run(job_id, stage_results.get("preflight_validator"), params)
            elif stage_name == "transcription_engine":
                result = processor.run(job_id, stage_results.get("video_ingestor"), params)
            elif stage_name == "world_class_highlights_extractor":
                print(f"   🌟 Using advanced highlights detection algorithm")
                print(f"   🎯 Finding the most engaging moments automatically")
                result = processor.run(job_id, stage_results.get("transcription_engine"),
                                     stage_results.get("video_ingestor"), params)
            elif stage_name == "qa_highlights_extractor":
                result = processor.run(job_id, stage_results.get("world_class_highlights_extractor"),
                                     stage_results.get("transcription_engine"), params)
            elif stage_name == "qa_pair_segment_extractor":
                # Use advanced highlights result for Q&A extraction
                advanced_result = stage_results.get("world_class_highlights_extractor")
                qa_highlights_result = stage_results.get("qa_highlights_extractor")

                print(f"   🎬 Using advanced highlights for Q&A extraction")
                highlights_count = advanced_result.get('highlights_count', 0) if advanced_result else 0
                print(f"   ✨ Found {highlights_count} advanced highlights")
                result = processor.run(job_id, qa_highlights_result,
                                     stage_results.get("transcription_engine"),
                                     stage_results.get("video_ingestor"), params)
            elif stage_name == "clip_renderer":
                # Use advanced highlights directly for clip rendering (bypass QA filtering)
                advanced_result = stage_results.get("world_class_highlights_extractor")
                highlights_count = advanced_result.get('highlights_count', 0) if advanced_result else 0
                print(f"   🌟 Using ALL {highlights_count} advanced highlights for rendering")
                print(f"   🎬 Bypassing QA filtering to preserve all optimal scenes")
                # Pass advanced_result as qa_result (last parameter) since it has clip_windows
                # Use dummy aligned_result since we're not using shot boundary aligner
                dummy_aligned_result = {'aligned_path': None}
                result = processor.run(job_id, dummy_aligned_result,
                                     stage_results.get("video_ingestor"), params, advanced_result)
            elif stage_name == "reframer":
                # Use temporal tracking if enabled for optimal highlights
                if params.get("use_temporal_tracking", False):
                    print(f"   🎯 Using temporal face tracking for optimal highlights")
                    print(f"   📊 Detection interval: {params.get('temporal_detection_interval', 1.0)}s")
                    print(f"   🎭 Backend: {params.get('face_detection_backend', 'mediapipe')}")
                    print(f"   🔍 Confidence: {params.get('face_detection_confidence', 0.4)}")
                    result = processor.process_with_temporal_tracking(job_id, stage_results.get("clip_renderer"), params)
                else:
                    result = processor.run(job_id, stage_results.get("clip_renderer"), params)
            elif stage_name == "caption_composer":
                result = processor.run(job_id, stage_results.get("reframer"),
                                     stage_results.get("transcription_engine"), params)
            elif stage_name == "deliverables_publisher":
                result = processor.run(job_id, stage_results.get("caption_composer"), params)
            elif stage_name == "cleanup_metrics":
                result = processor.run(job_id, stage_results.get("deliverables_publisher"), params)
            else:
                raise ValueError(f"Unknown stage: {stage_name}")

            # Store result with validation
            if not isinstance(result, dict):
                error_msg = f"Stage {stage_name} returned invalid result type: {type(result)}"
                logger.error(error_msg)
                raise ValueError(error_msg)

            stage_results[stage_name] = result
            logger.info(f"Stored result for stage: {stage_name}")

            # Check if stage failed with detailed error reporting
            status = result.get('status', 'unknown')
            if status != 'completed':
                # Handle both 'error' (singular) and 'errors' (plural) fields
                error_details = result.get('error')
                if not error_details and 'errors' in result:
                    # Join multiple errors into a single string
                    errors_list = result.get('errors', [])
                    if isinstance(errors_list, list):
                        error_details = '; '.join(errors_list)
                    else:
                        error_details = str(errors_list)

                if not error_details:
                    error_details = 'Unknown error'

                error_msg = f"Stage {stage_name} failed with status '{status}': {error_details}"
                logger.error(error_msg)

                # Log additional context if available
                if 'metadata' in result:
                    logger.error(f"Stage metadata: {result['metadata']}")

                raise ValueError(error_msg)

            print(f"   ✅ Status: {status}")

            # Log stage completion metrics if available
            if 'duration' in result:
                logger.info(f"Stage {stage_name} completed in {result['duration']:.2f}s")
            if 'highlights_count' in result:
                logger.info(f"Stage {stage_name} generated {result['highlights_count']} highlights")

        total_time = time.time() - start_time

        # Create highlights directory with separate clips if requested
        highlights_dir = None
        if params.get("create_highlights_directory", False):
            highlights_dir = create_highlights_directory(job_id, stage_results)
            if highlights_dir:
                print(f"📂 Created highlights directory: {highlights_dir}")
                print(f"🎬 All clips saved as separate files for easy access!")

        # Generate viral strategy if enabled
        if params.get("enable_viral_strategy", False) and highlights_dir:
            print(f"🚀 Generating viral strategy with JSON output...")
            try:
                from viral_strategy import generate_viral_strategy_for_job
                viral_model = params.get("viral_strategy_model", "gpt-4o-mini")
                strategy_path = generate_viral_strategy_for_job(job_id, highlights_dir, viral_model)

                if strategy_path:
                    print(f"✅ Viral strategy generated successfully!")
                    print(f"📄 Strategy file: {strategy_path}")

                    # Check for JSON file
                    json_path = os.path.join(highlights_dir, "viral_strategy.json")
                    if os.path.exists(json_path):
                        print(f"📊 JSON strategy file: {json_path}")
                        # Show file size
                        file_size = os.path.getsize(json_path)
                        print(f"📈 JSON file size: {file_size:,} bytes")
                    else:
                        print(f"⚠️  JSON strategy file not found at: {json_path}")
                else:
                    print(f"❌ Failed to generate viral strategy")
            except ImportError as e:
                print(f"⚠️  Viral strategy module not available: {e}")
            except Exception as e:
                print(f"❌ Error generating viral strategy: {e}")

        print("=" * 80)
        print(f"🌟 Advanced Highlights Pipeline Completed Successfully!")
        print(f"⏱️ Total Time: {total_time:.2f} seconds")
        print(f"📁 Job ID: {job_id}")
        print(f"🎬 Optimal scenes automatically detected and processed!")
        if params.get("enable_viral_strategy", False):
            print(f"🚀 Viral marketing strategy generated with JSON output!")
        print(f"💰 Streamlined pipeline saved time and money!")

        return stage_results

    except Exception as e:
        print(f"❌ Pipeline Failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def test_multi_face_video():
    """Test the advanced pipeline with the two-face video sample"""
    print("🧪 Testing Advanced Multi-Face Video Processing...")

    # Use the sample video with two faces
    video_path = "tests/sample/one_video_two_faces.mp4"

    if not os.path.exists(video_path):
        print(f"❌ Test video not found: {video_path}")
        return False

    print(f"📹 Testing with: {video_path}")
    print(f"🌟 Using advanced highlights detection with captions enabled")
    print(f"🎬 Expected: ONE highest-scoring highlight with optimal vertical cropping for two faces")
    print(f"📝 Captions: YouTube Shorts style with keyword highlighting")
    print(f"⚡ Testing mode: Generate only 1 highlight (fastest testing)")
    print("=" * 60)

    # Test parameters optimized for advanced detection with temporal tracking
    params = {
        "max_duration": 30,  # Shorter duration for testing
        "target_length": 30,
        "context_window": 5,
        "chronological_order": True,
        "output_format": "vertical",  # Test vertical format specifically
        "skip_captions": False,  # Enable captions for testing
        "add_progress_bar": False,
        "separate_clips": True,
        "create_highlights_directory": True,

        # TESTING OPTIMIZATION: Generate only the highest scoring highlight
        "max_highlights": 1,  # Limit to 1 highlight for faster testing
        "testing_mode": True,  # Enable testing mode for faster processing

        # Enhanced captioning configuration for testing
        "caption_style": "youtube_shorts",  # Use YouTube Shorts style for testing
        "caption_keywords": ["question", "answer", "interesting", "important", "amazing", "incredible"],  # Keywords for highlighting
        "caption_highlight_color": "#FFDA00",  # Yellow highlight color
        "caption_word_timing": True,  # Enable word-level timing for better synchronization
        "caption_accessibility": True,  # Enable accessibility-compliant captions
        "caption_formats": ["ass", "srt", "json"],  # Generate multiple caption formats
        "enable_smart_positioning": True,  # Enable face-aware caption positioning

        # Advanced detection optimization
        "prioritize_qa_segments": True,
        "emotion_weight": 0.3,
        "novelty_weight": 0.2,
        "quality_threshold": 0.6,

        # Temporal face tracking for optimal multi-face detection
        "use_temporal_tracking": True,
        "temporal_detection_interval": 1.0,  # Detect faces every second
        "temporal_smoothing_factor": 0.3,
        "face_detection_backend": "mediapipe",
        "face_detection_confidence": 0.4,
        "use_existing_tracking": False
    }

    try:
        # Use caption keywords for highlighting in captions (advanced detection doesn't need them for scene detection)
        keywords = ["question", "answer", "interesting", "important", "amazing", "incredible"]
        result = run_sequential_pipeline_with_resume(video_path, keywords, params, resume=False)

        if result:
            print("✅ Multi-face video processing completed successfully!")
            print("🎭 Check the output for improved face tracking with two people")
            print("📝 Captions have been generated with YouTube Shorts style and keyword highlighting")
            print("🧪 Testing mode: Only the highest-scoring highlight was processed for fast testing")
            return True
        else:
            print("❌ Multi-face video processing failed")
            return False

    except Exception as e:
        print(f"❌ Error during multi-face testing: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def generate_viral_strategy_for_specific_clip(job_id: str, clip_number: int, viral_model: str = "gpt-4o-mini") -> bool:
    """
    Generate viral strategy for a specific clip from an existing job

    Args:
        job_id: Existing job ID
        clip_number: Clip number to generate strategy for
        viral_model: OpenAI model to use for generation

    Returns:
        True if successful, False otherwise
    """
    try:
        # Validate job exists
        job_dir = os.path.join(PIPELINE_OUTPUT_DIR, job_id)
        if not os.path.exists(job_dir):
            print(f"❌ Job directory not found: {job_dir}")
            return False

        # Look for highlights directory
        highlights_dir = os.path.join(job_dir, "highlights")
        if not os.path.exists(highlights_dir):
            print(f"❌ Highlights directory not found: {highlights_dir}")
            return False

        print(f"🎯 Generating viral strategy for specific clip...")
        print(f"📁 Job ID: {job_id}")
        print(f"🎬 Clip Number: {clip_number}")
        print(f"🤖 Model: {viral_model}")
        print("=" * 60)

        # Import viral strategy generator
        from viral_strategy import ViralStrategyGenerator

        # Initialize generator
        generator = ViralStrategyGenerator(openai_model=viral_model)

        # Load highlights data
        highlights_data = generator._load_highlights_data(highlights_dir)
        if not highlights_data:
            print(f"❌ No highlights data found in: {highlights_dir}")
            return False

        # Validate clip number
        if clip_number < 0 or clip_number >= len(highlights_data):
            print(f"❌ Invalid clip number {clip_number}. Available clips: 0-{len(highlights_data)-1}")
            return False

        # Get the specific highlight
        highlight = highlights_data[clip_number]
        clip_filename = f"reframed_clip_{clip_number:03d}_temporal_reframed.mp4"

        # Extract content information
        content_text = highlight.get("text", "")
        duration = highlight.get("duration", 0)
        score = highlight.get("final_score", 0)

        print(f"🎬 Clip: {clip_filename}")
        print(f"⏱️ Duration: {duration:.1f}s")
        print(f"📊 Score: {score:.3f}")
        print(f"📝 Content preview: {content_text[:100]}...")
        print()

        # Generate strategy for this specific clip
        print(f"🚀 Generating viral strategy...")
        strategy = generator.generate_clip_strategy(
            clip_number=clip_number,
            clip_filename=clip_filename,
            content_text=content_text,
            duration=duration,
            score=score
        )

        # Create strategy document for single clip
        strategy_document = generator.create_strategy_document([strategy], job_id)

        # Save strategy files with clip-specific naming
        highlights_path = Path(highlights_dir)
        strategy_path = highlights_path / f"viral_strategy_clip_{clip_number:03d}.md"
        json_strategy_path = highlights_path / f"viral_strategy_clip_{clip_number:03d}.json"

        # Save markdown file
        strategy_path.write_text(strategy_document, encoding='utf-8')

        # Save JSON file
        strategy_json_data = {
            "job_id": job_id,
            "target_clip_number": clip_number,
            "total_clips_in_job": len(highlights_data),
            "generation_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "api_requests_made": generator.api_request_count,
            "cost_optimization": "single_clip_focus",
            "clip": strategy
        }

        json_strategy_path.write_text(
            json.dumps(strategy_json_data, indent=2, ensure_ascii=False),
            encoding='utf-8'
        )

        print(f"✅ Viral strategy generated successfully!")
        print(f"📄 Strategy file: {strategy_path}")
        print(f"📊 JSON strategy file: {json_strategy_path}")

        # Show file sizes
        md_size = os.path.getsize(strategy_path)
        json_size = os.path.getsize(json_strategy_path)
        print(f"📈 Markdown file size: {md_size:,} bytes")
        print(f"📈 JSON file size: {json_size:,} bytes")

        # Show viral score if available
        viral_score = strategy.get('viral_analysis', {}).get('viral_potential_score', 'N/A')
        if viral_score != 'N/A':
            print(f"🔥 Viral Potential Score: {viral_score}/100")

        return True

    except ImportError as e:
        print(f"⚠️  Viral strategy module not available: {e}")
        return False
    except Exception as e:
        print(f"❌ Error generating viral strategy for clip {clip_number}: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 Starting Sequential Pipeline with Resume...")
    parser = argparse.ArgumentParser(description='AI-Powered Advanced Scenes Extractor - Find the Most Engaging Moments!')
    parser.add_argument('video_path', nargs='?', help='Path to the input video file (optional for test mode)')
    parser.add_argument('keywords', nargs='?', help='Optional keywords to focus on specific topics (if not provided, will find ALL optimal scenes automatically)')
    parser.add_argument('--test-multi-face', action='store_true', help='Run multi-face video test with sample video')
    parser.add_argument('--resume', action='store_true', help='Enable resume functionality to save money and time')
    parser.add_argument('--no-resume', action='store_true', help='Disable resume functionality')
    parser.add_argument('--max-duration', type=int, default=60, help='Maximum video duration')
    parser.add_argument('--min-duration', type=int, default=10, help='Minimum video duration')
    parser.add_argument('--context-window', type=int, default=10, help='Context window size')
    parser.add_argument('--output-format', choices=['original', 'vertical', 'square'], default='vertical',
                       help='Output video format: original (no reframing), vertical (9:16), or square (1:1)')
    parser.add_argument('--separate-clips', action='store_true', default=True,
                       help='Save clips as separate files in highlights directory (default: True)')
    parser.add_argument('--no-separate-clips', action='store_true',
                       help='Disable separate clips and create concatenated video only')
    parser.add_argument('--temporal-tracking', action='store_true', default=True,
                       help='Enable temporal face tracking for optimal highlights (default: True)')
    parser.add_argument('--no-temporal-tracking', action='store_true',
                       help='Disable temporal tracking and use basic reframing only')
    parser.add_argument('--detection-interval', type=float, default=1.0,
                       help='Face detection interval in seconds (default: 1.0)')
    parser.add_argument('--face-confidence', type=float, default=0.4,
                       help='Face detection confidence threshold (default: 0.4)')

    # QA highlights options
    parser.add_argument('--enable-qa', action='store_true',
                       help='Enable QA highlights extraction (optional)')
    parser.add_argument('--disable-qa', action='store_true', default=True,
                       help='Disable QA highlights extraction (default)')

    # Viral strategy options
    parser.add_argument('--enable-viral-strategy', action='store_true', default=True,
                       help='Enable viral strategy generation with JSON output (default: True)')
    parser.add_argument('--disable-viral-strategy', action='store_true',
                       help='Disable viral strategy generation')
    parser.add_argument('--viral-model', type=str, default='gpt-4o-mini',
                       help='OpenAI model for viral strategy generation (default: gpt-4o-mini)')

    # Specific clip viral strategy options
    parser.add_argument('--job-id', type=str,
                       help='Existing job ID to generate viral strategy for a specific clip')
    parser.add_argument('--clip-number', type=int,
                       help='Clip number to generate viral strategy for (requires --job-id)')

    # Caption options
    parser.add_argument('--enable-captions', action='store_true', default=True,
                       help='Enable video captions (default: True)')
    parser.add_argument('--disable-captions', action='store_true',
                       help='Disable video captions')
    parser.add_argument('--caption-style', type=str, default='youtube_shorts',
                       choices=['youtube_shorts', 'tiktok', 'instagram_reels', 'professional'],
                       help='Caption style for videos (default: youtube_shorts)')
    parser.add_argument('--caption-highlight-color', type=str, default='#FFDA00',
                       help='Color for highlighting keywords in captions (default: #FFDA00)')

    # Testing optimization options
    parser.add_argument('--max-highlights', type=int, default=None,
                       help='Maximum number of highlights to generate (default: unlimited, use 1 for fast testing)')
    parser.add_argument('--testmode', action='store_true',
                       help='Enable test mode: generate maximum 1 highlight video for fast testing (default: production mode with up to 100 highlights)')

    print("📋 Parsing arguments...")
    args = parser.parse_args()
    print(f"✅ Arguments parsed: {args}")

    # Handle test mode
    if args.test_multi_face:
        print("🧪 Running multi-face video test...")
        success = test_multi_face_video()
        if success:
            print("✅ Multi-face test completed successfully!")
            sys.exit(0)
        else:
            print("❌ Multi-face test failed!")
            sys.exit(1)

    # Handle specific clip viral strategy generation
    if args.job_id and args.clip_number is not None:
        print("🎯 Specific clip viral strategy mode detected!")
        print(f"📁 Job ID: {args.job_id}")
        print(f"🎬 Clip Number: {args.clip_number}")
        print("=" * 60)

        success = generate_viral_strategy_for_specific_clip(
            job_id=args.job_id,
            clip_number=args.clip_number,
            viral_model=args.viral_model
        )

        if success:
            print("✅ Specific clip viral strategy generated successfully!")
            sys.exit(0)
        else:
            print("❌ Specific clip viral strategy generation failed!")
            sys.exit(1)

    # Validate job_id and clip_number are used together
    if args.job_id and args.clip_number is None:
        print("❌ Error: --clip-number is required when --job-id is provided")
        parser.print_help()
        sys.exit(1)

    if args.clip_number is not None and not args.job_id:
        print("❌ Error: --job-id is required when --clip-number is provided")
        parser.print_help()
        sys.exit(1)

    # Validate required arguments for normal mode
    if not args.video_path:
        print("❌ Error: video_path is required unless using --test-multi-face or --job-id with --clip-number")
        parser.print_help()
        sys.exit(1)

    video_path = args.video_path

    # Handle keywords - focus on finding the best scenes automatically
    if args.keywords:
        keywords = [k.strip() for k in args.keywords.split(',')]
        print(f"🎯 Using provided keywords: {', '.join(keywords)}")
        print(f"🎬 Will find optimal scenes related to these topics")
    else:
        # Use a comprehensive set of quality indicators to find the optimal scenes
        keywords = [
            # Engagement indicators
            "question", "answer", "interesting", "important", "amazing", "incredible",
            "surprising", "shocking", "wow", "really", "actually", "exactly",

            # Content quality indicators
            "key", "main", "point", "significant", "crucial", "essential", "critical",
            "fundamental", "core", "primary", "major", "vital",

            # Conversation dynamics
            "discussion", "conversation", "interview", "talk", "explain", "describe",
            "tell", "story", "example", "experience", "happened", "remember",

            # Emotional engagement
            "love", "hate", "feel", "think", "believe", "opinion", "perspective",
            "excited", "passionate", "frustrated", "happy", "sad", "angry",

            # Learning and insights
            "learn", "understand", "realize", "discover", "find", "know", "insight",
            "lesson", "advice", "tip", "secret", "truth", "fact", "research"
        ]
        print(f"🎬 Finding ALL the optimal scenes in your video automatically!")
        print(f"🤖 Using AI-powered scene detection with {len(keywords)} quality indicators")
        print(f"✨ Will identify the most engaging, emotional, and valuable moments")
        print(f"🎯 No manual keyword selection needed - the AI will find the gems!")

    # Handle resume logic - default to enabled, but allow explicit control
    if args.resume:
        resume = True
    elif args.no_resume:
        resume = False
    else:
        resume = False  # Default to enabled to save money and time

    # Handle separate clips logic
    if args.no_separate_clips:
        separate_clips = False
        create_highlights_dir = False
    else:
        separate_clips = args.separate_clips
        create_highlights_dir = True

    # Handle temporal tracking logic
    if args.no_temporal_tracking:
        use_temporal_tracking = False
    else:
        use_temporal_tracking = args.temporal_tracking

    # Handle QA highlights logic
    if args.enable_qa:
        enable_qa_highlights = True
    else:
        enable_qa_highlights = False  # Default to disabled

    # Handle viral strategy logic
    if args.disable_viral_strategy:
        enable_viral_strategy = False
    else:
        # Enable viral strategy if explicitly enabled OR if both job_id and clip_number are provided
        enable_viral_strategy = args.enable_viral_strategy or (args.job_id and args.clip_number is not None)

    # Handle captions logic
    if args.disable_captions:
        enable_captions = False
    else:
        enable_captions = args.enable_captions  # Default to True

    # Handle testing mode and max highlights logic
    if args.testmode:
        max_highlights = 1  # Force to 1 highlight in testing mode
        testing_mode = True
        logger.info("Test mode enabled: maximum 1 highlight video will be generated")
    elif args.max_highlights:
        max_highlights = args.max_highlights
        testing_mode = max_highlights <= 3  # Consider it testing mode if <= 3 highlights
        if testing_mode:
            logger.info(f"Testing mode enabled: maximum {max_highlights} highlight videos will be generated")
        else:
            logger.info(f"Production mode enabled: maximum {max_highlights} highlight videos will be generated")
    else:
        max_highlights = 100  # Default production mode limit
        testing_mode = False
        logger.info("Production mode enabled: maximum 100 highlight videos can be generated")

    # Parameters optimized for finding the optimal scenes automatically
    params = {
        "keywords": keywords,  # Comprehensive quality indicators for optimal scene detection
        "max_duration": args.max_duration,
        "min_duration": args.min_duration,  # Add minimum duration parameter
        "context_window": args.context_window,
        "chronological_order": True,
        "output_format": args.output_format,  # Use command-line specified format

        # Enhanced captioning configuration
        "skip_captions": not enable_captions,  # Enable/disable captions based on user preference
        "caption_style": args.caption_style,  # Use user-specified caption style
        "caption_keywords": keywords,  # Pass keywords for highlighting in captions
        "caption_highlight_color": args.caption_highlight_color,  # User-specified highlight color
        "caption_word_timing": True,  # Enable word-level timing for better synchronization
        "caption_accessibility": True,  # Enable accessibility-compliant captions
        "caption_formats": ["ass", "srt", "json"],  # Generate multiple caption formats

        "add_progress_bar": False,  # Disable progress bar to simplify concatenation
        "separate_clips": separate_clips,  # Keep clips separate instead of concatenating
        "create_highlights_directory": create_highlights_dir,  # Create unified highlights directory

        # Advanced scene detection optimization
        "prioritize_qa_segments": True,  # Prioritize question-answer segments as they're often engaging
        "emotion_weight": 0.3,  # Increase emotion scoring weight for engaging content
        "novelty_weight": 0.2,  # Increase novelty weight to find unique moments
        "quality_threshold": 0.6,  # Higher quality threshold for optimal scenes

        # Testing optimization
        "max_highlights": max_highlights,  # Limit number of highlights for faster testing
        "testing_mode": testing_mode,  # Enable testing mode optimizations

        # QA highlights configuration
        "enable_qa_highlights": enable_qa_highlights,  # Whether to run QA extraction stages

        # Viral strategy configuration
        "enable_viral_strategy": enable_viral_strategy,  # Enable viral strategy generation with JSON output
        "viral_strategy_model": args.viral_model,  # Cost-effective model for viral strategy
        "target_clip_number": args.clip_number,  # Target specific clip number when provided

        # Caption configuration
        "enable_captions": enable_captions,  # Whether to generate captions
        "caption_style": args.caption_style,  # Caption style for videos
        "caption_highlight_color": args.caption_highlight_color,  # Color for highlighting keywords

        # Temporal face tracking parameters for optimal highlights (like temporal_clips_test)
        "use_temporal_tracking": use_temporal_tracking,  # Enable temporal tracking for smooth face following
        "temporal_detection_interval": args.detection_interval,  # Detect faces every second (same as temporal test)
        "temporal_smoothing_factor": 0.3,  # Smooth camera movements
        "face_detection_backend": "mediapipe",  # Use MediaPipe for best results
        "face_detection_confidence": args.face_confidence,  # Lower threshold for better detection (same as temporal test)
        "use_existing_tracking": False  # Generate fresh tracking data
    }

    print(f"💰 Resume functionality: {'ENABLED' if resume else 'DISABLED'}")
    print(f"📂 Separate clips: {'ENABLED' if separate_clips else 'DISABLED'}")
    if separate_clips:
        print(f"🎬 Optimal scenes will be saved as separate clips in highlights directory")
        print(f"✨ Optimal for social media - each clip is a standalone gem!")
    else:
        print(f"🎞️ Optimal scenes will be concatenated into a single highlight reel")

    print(f"🎯 QA highlights: {'ENABLED' if enable_qa_highlights else 'DISABLED'}")
    if enable_qa_highlights:
        print(f"   📝 Will extract question-answer style highlights")
        print(f"   🔍 Adds additional Q&A processing to advanced highlights")
    else:
        print(f"   🌟 Using pure advanced highlights only (faster, simpler)")

    # Show testing mode and highlights limit
    if testing_mode:
        if max_highlights == 1:
            print(f"🧪 Testing mode: ENABLED (generating only the highest-scoring highlight)")
        else:
            print(f"🧪 Testing mode: ENABLED (generating up to {max_highlights} highlights)")
        print(f"   ⚡ Optimized for fast development and testing")
        print(f"   🎯 Will analyze all segments but only process top-scoring highlight(s)")
    else:
        if max_highlights:
            print(f"🎬 Highlights limit: {max_highlights} (custom limit)")
        else:
            print(f"🎬 Highlights limit: UNLIMITED (will find all optimal scenes)")

    print(f"🚀 Viral strategy: {'ENABLED' if enable_viral_strategy else 'DISABLED'}")
    if enable_viral_strategy:
        print(f"   📊 Will generate viral marketing strategy with JSON output")
        print(f"   🤖 Using model: {args.viral_model}")
        print(f"   💡 Includes titles, hashtags, descriptions, and viral analysis")
        if args.clip_number is not None:
            print(f"   🎯 Target clip: {args.clip_number} (specific clip focus)")
        else:
            print(f"   🎬 Target: All clips (comprehensive strategy)")
    else:
        print(f"   ⚠️  No viral strategy will be generated")

    print(f"🧪 Processing mode: {'TEST MODE' if testing_mode else 'PRODUCTION MODE'}")
    if testing_mode:
        print(f"   🎯 Maximum highlights: {max_highlights} (optimized for fast testing)")
        print(f"   ⚡ Reduced processing time and resource usage")
        print(f"   🔬 Perfect for development and testing workflows")
    else:
        print(f"   🏭 Maximum highlights: {max_highlights} (full production processing)")
        print(f"   🎬 Complete highlight extraction for comprehensive results")
        print(f"   💼 Optimized for production deployment and full video analysis")

    print(f"📝 Video captions: {'ENABLED' if enable_captions else 'DISABLED'}")
    if enable_captions:
        print(f"   🎨 Caption style: {args.caption_style}")
        print(f"   🌈 Highlight color: {args.caption_highlight_color}")
        print(f"   📱 Optimized for social media platforms")
        print(f"   ♿ Accessibility-compliant captions included")
        print(f"   📄 Multiple formats: ASS, SRT, JSON")
    else:
        print(f"   ⚠️  No captions will be generated")

    # Show temporal tracking status
    if params.get("use_temporal_tracking", False):
        print(f"🎯 Temporal face tracking: ENABLED for perfect scene framing")
        print(f"   📊 Detection interval: {params.get('temporal_detection_interval', 1.0)}s")
        print(f"   🎭 Backend: {params.get('face_detection_backend', 'mediapipe')}")
        print(f"   🔍 Confidence: {params.get('face_detection_confidence', 0.4)}")
        print(f"   🎬 Will keep speakers perfectly centered in every scene!")
    else:
        print(f"⚠️  Temporal face tracking: DISABLED (basic reframing only)")

    # Display output format information
    format_info = {
        'original': '📺 Original format (no reframing)',
        'vertical': '📱 Vertical format (9:16 aspect ratio) - Perfect for YouTube Shorts, Instagram Reels, TikTok',
        'square': '⬜ Square format (1:1 aspect ratio) - Perfect for Instagram posts'
    }
    print(f"🎬 Output format: {format_info.get(args.output_format, args.output_format)}")

    result = run_sequential_pipeline_with_resume(video_path, keywords, params, resume)

    if result:
        print("✅ Best scenes extraction completed successfully!")
        print("🎬 Your video's most engaging moments have been identified and processed!")
        print("✨ Check the highlights directory for your best scenes!")
        sys.exit(0)
    else:
        print("❌ Best scenes extraction failed!")
        sys.exit(1)
